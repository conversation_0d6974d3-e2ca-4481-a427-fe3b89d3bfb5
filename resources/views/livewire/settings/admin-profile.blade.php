<div>
    <div class="settings-heading">
        <h1>Profile Settings</h1>
        <p>Update your name and email address</p>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card mb-4">
                <div class="card-body">
                    <form wire:submit="updateProfileInformation">
                        <div class="mb-3">
                            <label for="name" class="form-label">Name</label>
                            <input wire:model="name" type="text" class="form-control" id="name" required autofocus>
                            @error('name') <div class="text-danger mt-1">{{ $message }}</div> @enderror
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input wire:model="email" type="email" class="form-control" id="email" required>
                            @error('email') <div class="text-danger mt-1">{{ $message }}</div> @enderror

                            @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && !auth()->user()->hasVerifiedEmail())
                                <div class="alert alert-warning mt-3 d-flex align-items-center" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    <div>
                                        Your email address is unverified.
                                        <a href="#" wire:click.prevent="resendVerificationNotification" class="alert-link">
                                            Click here to re-send the verification email.
                                        </a>
                                    </div>
                                </div>

                                @if (session('status') === 'verification-link-sent')
                                    <div class="alert alert-success mt-3 d-flex align-items-center" role="alert">
                                        <i class="fas fa-check-circle me-2"></i>
                                        <div>
                                            A new verification link has been sent to your email address.
                                        </div>
                                    </div>
                                @endif
                            @endif
                        </div>

                        <div class="d-flex justify-content-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <div class="card mt-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0">Password Reset</h5>
                </div>
                <div class="card-body">
                    <p>If you need to reset your password, you can request a password reset link to be sent to your email address.</p>
                    <button type="button" class="btn btn-primary" wire:click="sendPasswordResetLink">
                        <i class="fas fa-key me-2"></i> Send Password Reset Link
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>
