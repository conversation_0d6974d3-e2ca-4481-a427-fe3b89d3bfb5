@section('content')
<!-- Content Header (Page header) -->
<div class="content-header">
    <div class="container-fluid">
        <div class="row mb-2">
            <div class="col-sm-6">
                <h1 class="m-0">Profile Settings</h1>
            </div>
            <div class="col-sm-6">
                <ol class="breadcrumb float-sm-right">
                    <li class="breadcrumb-item"><a href="{{ route('admin.dashboard') }}">Dashboard</a></li>
                    <li class="breadcrumb-item active">Profile Settings</li>
                </ol>
            </div>
        </div>
    </div>
</div>

<!-- Main content -->
<section class="content">
    <div class="container-fluid">
        <!-- Success/Error Messages -->
        @if (session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle mr-2"></i>
                {{ session('success') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        @if (session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-circle mr-2"></i>
                {{ session('error') }}
                <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
        @endif

        <div class="row">
            <div class="col-md-8">
                <!-- Profile Information Card -->
                <div class="card card-primary">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-user mr-2"></i>
                            Profile Information
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted mb-4">Update your name and email address</p>

                        <form wire:submit="updateProfileInformation">
                            <div class="form-group">
                                <label for="name">Name</label>
                                <input wire:model="name" type="text" class="form-control" id="name" required autofocus>
                                @error('name')
                                    <div class="text-danger mt-1">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror
                            </div>

                            <div class="form-group">
                                <label for="email">Email</label>
                                <input wire:model="email" type="email" class="form-control" id="email" required>
                                @error('email')
                                    <div class="text-danger mt-1">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        {{ $message }}
                                    </div>
                                @enderror

                                @if (auth()->user() instanceof \Illuminate\Contracts\Auth\MustVerifyEmail && !auth()->user()->hasVerifiedEmail())
                                    <div class="alert alert-warning mt-3" role="alert">
                                        <i class="fas fa-exclamation-triangle mr-2"></i>
                                        <strong>Email Verification Required:</strong>
                                        Your email address is unverified.
                                        <a href="#" wire:click.prevent="resendVerificationNotification" class="alert-link">
                                            Click here to re-send the verification email.
                                        </a>
                                    </div>

                                    @if (session('status') === 'verification-link-sent')
                                        <div class="alert alert-success mt-3" role="alert">
                                            <i class="fas fa-check-circle mr-2"></i>
                                            <strong>Verification Link Sent:</strong>
                                            A new verification link has been sent to your email address.
                                        </div>
                                    @endif
                                @endif
                            </div>

                            <div class="form-group">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-save mr-2"></i> Save Changes
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>

            <div class="col-md-4">
                <!-- Password Reset Card -->
                <div class="card card-warning">
                    <div class="card-header">
                        <h3 class="card-title">
                            <i class="fas fa-key mr-2"></i>
                            Password Reset
                        </h3>
                    </div>
                    <div class="card-body">
                        <p class="text-muted">If you need to reset your password, you can request a password reset link to be sent to your email address.</p>
                        <button type="button" class="btn btn-warning btn-block" wire:click="sendPasswordResetLink">
                            <i class="fas fa-key mr-2"></i> Send Password Reset Link
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>
@endsection
