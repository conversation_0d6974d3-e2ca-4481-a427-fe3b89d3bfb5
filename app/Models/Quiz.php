<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Quiz extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'landing_page_id',
        'title',
        'description',
        'scoring_rules',
        'is_active',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'scoring_rules' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the landing page that owns this quiz.
     */
    public function landingPage(): BelongsTo
    {
        return $this->belongsTo(LandingPage::class);
    }

    /**
     * Get the questions for this quiz.
     */
    public function questions(): HasMany
    {
        return $this->hasMany(QuizQuestion::class)->orderBy('sort_order');
    }

    /**
     * Get the categories for this quiz.
     */
    public function categories(): HasMany
    {
        return $this->hasMany(QuizCategory::class)->orderBy('sort_order');
    }

    /**
     * Get the active categories for this quiz.
     */
    public function activeCategories(): HasMany
    {
        return $this->categories()->where('is_active', true);
    }

    /**
     * Calculate the total possible score for this quiz.
     */
    public function getTotalPossibleScore(): int
    {
        return $this->questions()->sum('points');
    }

    /**
     * Calculate score from responses.
     */
    public function calculateScore(array $responses): int
    {
        $score = 0;
        $questions = $this->questions()->get();

        foreach ($questions as $question) {
            $response = $responses[$question->id] ?? null;

            if ($response !== null) {
                $score += $this->calculateQuestionScore($question, $response);
            }
        }

        return $score;
    }

    /**
     * Calculate score for a single question.
     */
    private function calculateQuestionScore(QuizQuestion $question, $response): int
    {
        switch ($question->type) {
            case 'multiple_choice':
            case 'single_choice':
                $options = $question->options;
                if (isset($options[$response]['points'])) {
                    return $options[$response]['points'];
                }
                break;

            case 'scale':
                // For scale questions, response is the scale value
                return (int) $response;

            case 'text':
                // Text questions get full points if answered
                return $response ? $question->points : 0;
        }

        return 0;
    }
}
