<?php

namespace App\Livewire\Settings;

use App\Models\User;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Session;
use Illuminate\Validation\Rule;
use Livewire\Component;

class AdminProfile extends Component
{
    public string $name = '';
    public string $email = '';

    /**
     * Mount the component.
     */
    public function mount(): void
    {
        $this->name = Auth::user()->name;
        $this->email = Auth::user()->email;
    }

    /**
     * Update the profile information for the currently authenticated user.
     */
    public function updateProfileInformation(): void
    {
        $user = Auth::user();

        $validated = $this->validate([
            'name' => ['required', 'string', 'max:255'],
            'email' => [
                'required',
                'string',
                'lowercase',
                'email',
                'max:255',
                Rule::unique(User::class)->ignore($user->id)
            ],
        ]);

        $user->fill($validated);

        if ($user->isDirty('email')) {
            $user->email_verified_at = null;
        }

        $user->save();

        session()->flash('success', 'Profile updated successfully.');
    }

    /**
     * Send an email verification notification to the current user.
     */
    public function resendVerificationNotification(): void
    {
        $user = Auth::user();

        if ($user->hasVerifiedEmail()) {
            $redirectRoute = $user->is_admin ?
                route('admin.dashboard', absolute: false) :
                route('home', absolute: false);

            $this->redirectIntended(default: $redirectRoute);

            return;
        }

        $user->sendEmailVerificationNotification();

        Session::flash('status', 'verification-link-sent');
    }

    /**
     * Send a password reset link to the user's email.
     */
    public function sendPasswordResetLink(): void
    {
        $status = \Illuminate\Support\Facades\Password::sendResetLink(
            ['email' => Auth::user()->email]
        );

        if ($status === \Illuminate\Support\Facades\Password::RESET_LINK_SENT) {
            session()->flash('success', 'Password reset link has been sent to your email.');
        } else {
            session()->flash('error', 'Unable to send password reset link. Please try again later.');
        }
    }

    /**
     * Render the component.
     */
    public function render()
    {
        return view('livewire.settings.admin-profile')
            ->layout('layouts.settings', ['title' => 'Profile Settings']);
    }
}
